# 共享RGB球面高斯模型 (Shared RGB Spherical Gaussian Model)

## 概述

这是对原始球面高斯模型的改进版本，主要特点是所有球面高斯轴共用一组RGB颜色参数，并使用sharpness作为权重来调制颜色，参考了`gaussian_model_simple.py`中`features_rest`的计算方法。

## 主要改进

### 1. 共享RGB设计
- **原始模型**: `_sg_rgb` 形状为 `[N, max_sg_degree, 3]`，每个高斯的每个轴都有独立的RGB
- **共享RGB模型**: `_sg_rgb` 形状为 `[max_sg_degree, 3]`，所有高斯共享相同的轴RGB

### 2. Sharpness权重调制
```python
# 获取归一化的sharpness作为权重 [N, max_sg_degree, 1]
sharpness_weights = self.get_sg_sharpness  

# 归一化sharpness权重（按轴维度）
sharpness_sum = torch.sum(sharpness_weights, dim=1, keepdim=True) + 1e-8
normalized_weights = sharpness_weights / sharpness_sum

# 共享RGB [max_sg_degree, 3] -> [1, max_sg_degree, 3]
shared_rgb = self._sg_rgb.unsqueeze(0)

# 使用sharpness权重调制共享RGB [N, max_sg_degree, 3]
weighted_rgb = shared_rgb * normalized_weights
```

### 3. 参数数量大幅减少
对于N个高斯点和max_sg_degree个轴：
- **原始模型**: `N × max_sg_degree × 3` 个RGB参数
- **共享RGB模型**: `max_sg_degree × 3` 个RGB参数
- **参数减少**: 约 `(N-1)/N × 100%`

## 文件结构

```
GaussianSpa/
├── scene/
│   ├── spherical_gaussian_model_cullSG.py          # 原始模型
│   └── spherical_gaussian_model_shared_rgb.py      # 新的共享RGB模型
├── train_imp_score_spherical_gaussian_cullSG.py    # 原始训练脚本
├── train_spherical_gaussian_shared_rgb.py          # 新的训练脚本
├── test_shared_rgb_model.py                        # 测试脚本
└── README_shared_rgb.md                            # 本文档
```

## 使用方法

### 1. 训练
```bash
python train_spherical_gaussian_shared_rgb.py \
    --source_path /path/to/your/data \
    --model_path /path/to/output \
    --imp_metric outdoor \
    --iterations 50000
```

### 2. 测试模型功能
```bash
python test_shared_rgb_model.py
```

### 3. 在代码中使用
```python
from scene.spherical_gaussian_model_shared_rgb import SphericalGaussianModelSharedRGB

# 创建模型
gaussians = SphericalGaussianModelSharedRGB(max_sg_degree=3)

# 从点云初始化
gaussians.create_from_pcd(pcd, spatial_lr_scale=1.0)

# 获取共享RGB调制后的颜色
rgb_colors = gaussians.get_sg_rgb  # [N, max_sg_degree, 3]
```

## 核心改动说明

### 1. 模型初始化
```python
# 原始: 每个高斯独立的RGB
self._sg_rgb = torch.empty(0)  # [N, total_bases, 3]

# 共享RGB: 所有高斯共享的RGB
self._sg_rgb = torch.empty(0)  # [max_sg_degree, 3]
```

### 2. RGB计算方法
```python
@property
def get_sg_rgb(self):
    """动态生成基于sharpness权重的RGB颜色"""
    # 获取sharpness权重并归一化
    sharpness_weights = self.get_sg_sharpness
    sharpness_sum = torch.sum(sharpness_weights, dim=1, keepdim=True) + 1e-8
    normalized_weights = sharpness_weights / sharpness_sum
    
    # 使用权重调制共享RGB
    shared_rgb = self._sg_rgb.unsqueeze(0)
    weighted_rgb = shared_rgb * normalized_weights
    
    return weighted_rgb
```

### 3. 训练优化器设置
```python
# 添加共享RGB参数到优化器
if self.max_sg_degree > 0:
    l.append({'params': [self._sg_rgb], 'lr': training_args.feature_lr, "name": "sg_rgb"})
```

### 4. 密度化处理
由于RGB现在是共享的，在densify_and_split和densify_and_clone中不再需要复制RGB参数：
```python
# 原始: 需要复制RGB
new_sg_rgb = self._sg_rgb[selected_pts_mask].repeat(N,1,1)

# 共享RGB: 不需要复制，因为是共享的
# new_sg_rgb 参数被移除
```

## 优势

1. **内存效率**: 大幅减少RGB参数数量
2. **训练效率**: 更少的参数意味着更快的训练速度
3. **一致性**: 共享RGB确保了轴间的颜色一致性
4. **灵活性**: 通过sharpness权重仍能实现个性化的颜色表现

## 注意事项

1. 确保在使用前已正确安装所有依赖
2. 共享RGB模型与原始模型的checkpoint不兼容
3. PLY文件格式有所变化，包含了共享RGB信息
4. 测试脚本需要CUDA环境（如果可用）

## 兼容性

- 与原始spherical_gaussian_renderer兼容
- 与现有的训练pipeline兼容
- 支持所有原始模型的功能（剪枝、密度化等）
